#!/bin/bash

# Unified Production Deployment Script for Propbolt Real Estate Platform
# Deploys Go backend (api.propbolt.com) and Next.js frontend (propbolt.com) to Google App Engine
# Includes comprehensive monitoring, logging, and health checks

set -e  # Exit on any error

echo "🚀 Propbolt Production Deployment - Unified Script"
echo "=================================================="
echo "Backend: api.propbolt.com | Frontend: propbolt.com"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if gcloud is installed and authenticated
print_status "Checking Google Cloud CLI setup..."
if ! command -v gcloud &> /dev/null; then
    print_error "Google Cloud CLI is not installed. Please install it first."
    exit 1
fi

# Get current project
PROJECT_ID=$(gcloud config get-value project 2>/dev/null)
if [ -z "$PROJECT_ID" ]; then
    print_error "No Google Cloud project set. Please run: gcloud config set project YOUR_PROJECT_ID"
    exit 1
fi

print_success "Using Google Cloud project: $PROJECT_ID"

# Phase 1: Backend Deployment
echo ""
echo "📦 PHASE 1: BACKEND DEPLOYMENT"
echo "=============================="

print_status "Cleaning Go dependencies..."
go mod tidy

print_status "Building Go application..."
go build -o propbolt

print_status "Setting up backend .gcloudignore..."
cp .gcloudignore-backend .gcloudignore

print_status "Deploying Go backend to default service..."
gcloud app deploy app.yaml --quiet

print_success "Backend deployed successfully!"

# Test backend health
print_status "Testing backend health (api.propbolt.com)..."
BACKEND_URL="https://api.propbolt.com"
if curl -s "$BACKEND_URL/status" | grep -q '"status": "ok"'; then
    print_success "Backend API health check passed!"
else
    print_warning "Backend health check failed, but continuing deployment..."
fi

# Test Service Health API
print_status "Testing Service Health API..."
if curl -s "$BACKEND_URL/health" >/dev/null 2>&1; then
    print_success "Service Health API responding!"
else
    print_warning "Service Health API not responding, but continuing..."
fi

# Phase 2: Frontend Deployment
echo ""
echo "🎨 PHASE 2: FRONTEND DEPLOYMENT"
echo "==============================="

print_status "Setting up frontend .gcloudignore..."
cp .gcloudignore-frontend .gcloudignore

print_status "Deploying Next.js frontend to frontend service..."
gcloud app deploy frontend-app.yaml --quiet

print_status "Deploying dispatch.yaml for routing..."
gcloud app deploy dispatch.yaml --quiet

print_success "Frontend deployed successfully!"

# Test frontend health
print_status "Testing frontend (propbolt.com)..."
FRONTEND_URL="https://propbolt.com"
if curl -s "$FRONTEND_URL/" | grep -q "Vacant Land Search"; then
    print_success "Frontend application health check passed!"
else
    print_warning "Frontend health check failed, but continuing..."
fi

# Phase 3: Monitoring and Logging Setup
echo ""
echo "📊 PHASE 3: MONITORING & LOGGING SETUP"
echo "======================================"

print_status "Setting up uptime monitoring..."

# Create uptime checks if they don't exist
if ! gcloud monitoring uptime list-configs | grep -q "Backend API Health Check"; then
    print_status "Creating backend uptime check..."
    gcloud monitoring uptime create "Backend API Health Check" \
        --resource-type=uptime-url \
        --resource-labels=host=api.propbolt.com,project_id=$PROJECT_ID \
        --protocol=https \
        --path=/status \
        --matcher-content='"status": "ok"' \
        --matcher-type=contains-string \
        --period=1 \
        --timeout=10 \
        --regions=usa-oregon,usa-iowa,usa-virginia
    print_success "Backend uptime check created!"
else
    print_success "Backend uptime check already exists!"
fi

if ! gcloud monitoring uptime list-configs | grep -q "Frontend App Health Check"; then
    print_status "Creating frontend uptime check..."
    gcloud monitoring uptime create "Frontend App Health Check" \
        --resource-type=uptime-url \
        --resource-labels=host=propbolt.com,project_id=$PROJECT_ID \
        --protocol=https \
        --path=/ \
        --matcher-content="Vacant Land Search" \
        --matcher-type=contains-string \
        --period=1 \
        --timeout=10 \
        --regions=usa-oregon,usa-iowa,usa-virginia
    print_success "Frontend uptime check created!"
else
    print_success "Frontend uptime check already exists!"
fi

# Enable logging APIs
print_status "Enabling Google Cloud Logging APIs..."
gcloud services enable logging.googleapis.com --quiet
gcloud services enable monitoring.googleapis.com --quiet

print_success "Monitoring and logging setup complete!"

# Phase 4: Cleanup and Summary
echo ""
echo "🧹 PHASE 4: CLEANUP & SUMMARY"
echo "============================="

print_status "Cleaning up temporary files..."
rm -f backend-uptime-config.yaml frontend-uptime-config.yaml
rm -f backend-alert-policy.yaml frontend-alert-policy.yaml

# Final status check
echo ""
echo "🎉 DEPLOYMENT COMPLETE!"
echo "======================"
echo ""
echo "📋 UNIFIED DEPLOYMENT SUMMARY:"
echo "==============================="
echo "🔧 Backend API:      https://api.propbolt.com"
echo "🌐 Frontend App:     https://propbolt.com"
echo "🔀 Routing:          dispatch.yaml configured"
echo ""
echo "📊 PRODUCTION MONITORING:"
echo "-------------------------"
echo "✅ Uptime monitoring: Backend + Frontend"
echo "✅ Google Cloud Logging: Enabled"
echo "✅ Service Health API: /health endpoint"
echo "✅ Auto-scaling: Backend (1-10), Frontend (1-5)"
echo ""
echo "🔍 ESSENTIAL COMMANDS:"
echo "----------------------"
echo "All logs:           gcloud app logs tail"
echo "Backend logs:       gcloud app logs tail -s default"
echo "Frontend logs:      gcloud app logs tail -s frontend"
echo "Services status:    gcloud app services list"
echo "Health check:       curl https://api.propbolt.com/status"
echo ""
echo "🌐 PRODUCTION URLS:"
echo "------------------"
echo "Frontend:           https://propbolt.com"
echo "Backend API:        https://api.propbolt.com"
echo "Service Health:     https://api.propbolt.com/health"
echo ""

print_success "🎉 Unified production deployment completed successfully!"
print_status "✅ Both services deployed with single script execution"
print_status "✅ Production-only configuration (no mock/dev data)"
print_status "✅ Comprehensive monitoring and logging enabled"
print_status "🚀 Platform ready for production use!"

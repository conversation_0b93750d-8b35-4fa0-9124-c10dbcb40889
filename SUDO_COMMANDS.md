# Sudo Commands for Propbolt Migration

## When to Use Sudo

Use `sudo` for commands that encounter permission errors. The migration script now automatically retries with sudo if needed.

## Manual Migration Commands with Sudo

### 1. Export Data from Neon

```bash
# Export schema
sudo pg_dump "postgresql://wonderland_owner:<EMAIL>/wonderland?sslmode=require" \
    --schema-only --no-owner --no-privileges > neon_schema.sql

# Export data
sudo pg_dump "postgresql://wonderland_owner:<EMAIL>/wonderland?sslmode=require" \
    --data-only --no-owner --no-privileges > neon_data.sql
```

### 2. Import Data to Cloud SQL

```bash
# Get Cloud SQL IP
CLOUD_SQL_IP=$(gcloud sql instances describe propbolt-postgres --format="value(ipAddresses[0].ipAddress)")

# Import schema
sudo psql "*****************************************************************/propbolt?sslmode=require" < neon_schema.sql

# Import data
sudo psql "*****************************************************************/propbolt?sslmode=require" < neon_data.sql
```

### 3. Test Database Connection

```bash
# Test connection
sudo psql "*****************************************************************/propbolt?sslmode=require" -c "SELECT version();"

# List tables
sudo psql "*****************************************************************/propbolt?sslmode=require" -c "\dt"

# Check data counts
sudo psql "*****************************************************************/propbolt?sslmode=require" -c "
SELECT 
    'users' as table_name, COUNT(*) as row_count FROM users
UNION ALL
SELECT 
    'properties' as table_name, COUNT(*) as row_count FROM properties
UNION ALL
SELECT 
    'watchlist_items' as table_name, COUNT(*) as row_count FROM watchlist_items
UNION ALL
SELECT 
    'search_queries' as table_name, COUNT(*) as row_count FROM search_queries;
"
```

### 4. File Operations with Sudo

```bash
# Update configuration files
sudo sed -i.bak "s|DATABASE_URL:.*|DATABASE_URL: \"***************************************************************************************************************************"|" app.yaml

# Create backup files
sudo cp app.yaml app.yaml.backup
sudo cp frontend-app.yaml frontend-app.yaml.backup

# Clean up temporary files
sudo rm -f neon_schema.sql neon_data.sql *.bak
```

### 5. Build and Deploy with Sudo

```bash
# Go build (if permission issues)
sudo go mod tidy
sudo go build -o propbolt

# NPM build (if permission issues)
sudo npm run build

# Deploy (if permission issues)
sudo ./deploy-production.sh
```

### 6. Google Cloud Commands

Note: Google Cloud CLI commands typically don't need sudo, but if you encounter permission issues:

```bash
# Cloud SQL operations
sudo gcloud sql instances create propbolt-postgres --database-version=POSTGRES_15 --tier=db-f1-micro --region=us-central1

sudo gcloud sql users set-password root --instance=propbolt-postgres --password=PropboltSecure2024!

sudo gcloud sql databases create propbolt --instance=propbolt-postgres

sudo gcloud sql users create propbolt_user --instance=propbolt-postgres --password=PropboltSecure2024!
```

## Troubleshooting Permission Issues

### Common Permission Errors and Solutions

1. **PostgreSQL Client Tools**:
   ```bash
   # If pg_dump fails
   sudo pg_dump [connection_string] [options]
   
   # If psql fails
   sudo psql [connection_string] [options]
   ```

2. **File System Operations**:
   ```bash
   # If file creation fails
   sudo touch filename
   sudo chmod 644 filename
   
   # If file editing fails
   sudo nano filename
   sudo vim filename
   ```

3. **Build Operations**:
   ```bash
   # If Go build fails
   sudo chown -R $USER:$USER /path/to/project
   go mod tidy
   go build
   ```

4. **NPM Operations**:
   ```bash
   # If npm fails
   sudo npm install
   sudo npm run build
   ```

## Security Notes

- Only use `sudo` when necessary
- The migration script automatically tries without sudo first
- After migration, ensure proper file permissions are restored
- Database access is restricted to App Engine only

## Updated Migration Script

The migration script (`migrate-to-cloudsql.sh`) now includes automatic sudo retry logic:
- Tries commands without sudo first
- Automatically retries with sudo if permission denied
- Provides clear warnings when using sudo
- Maintains security best practices

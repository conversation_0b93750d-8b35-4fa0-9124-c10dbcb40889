# 🏢 Propbolt - Vacant Land Search Platform

**Production Real Estate Admin Panel for Daytona Beach, Florida**

## 🌐 Production URLs
- **Frontend:** https://propbolt.com
- **Backend API:** https://api.propbolt.com

## 🚀 Unified Deployment
```bash
./deploy-production.sh
```

This single script handles:
- ✅ Go backend deployment to `api.propbolt.com`
- ✅ Next.js frontend deployment to `propbolt.com`
- ✅ Google Cloud monitoring and logging setup
- ✅ Health checks and uptime monitoring
- ✅ Auto-scaling configuration (Backend: 1-10, Frontend: 1-5)

## 📊 Essential Commands

### Logging
```bash
# View all logs
gcloud app logs tail

# Backend logs (api.propbolt.com)
gcloud app logs tail -s default

# Frontend logs (propbolt.com)
gcloud app logs tail -s frontend

# Live streaming
gcloud app logs tail --follow
```

### Service Management
```bash
# List services and versions
gcloud app services list
gcloud app versions list

# Health checks
curl https://api.propbolt.com/status
curl https://propbolt.com/
```

## 🔧 Production Stack
- **Backend:** Go 1.22 + Neon PostgreSQL + Smartproxy rotation
- **Frontend:** Next.js 20 + NextAuth.js + Mapbox GL JS
- **Infrastructure:** Google App Engine with auto-scaling
- **Monitoring:** Cloud Logging + Uptime checks + Error reporting

## 🌐 Quick Links
- **Cloud Console:** https://console.cloud.google.com/appengine
- **Logs:** https://console.cloud.google.com/logs/query
- **Monitoring:** https://console.cloud.google.com/monitoring

---
**🏢 Propbolt Production Platform - Unified deployment with Google Cloud**

/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: [
      'propbolt.com',
      'api.propbolt.com',
      'api.mapbox.com',
      'api.realestateapi.com'
    ],
  },
  env: {
    NEXT_PUBLIC_API_BASE_URL: 'https://api.propbolt.com',
    NEXT_PUBLIC_MAPBOX_TOKEN: 'pk.eyJ1Ijoic2F2ZXVuZGVyY29udHJhY3QiLCJhIjoiY202anNoNWtvMDRpMjJqb2x1MWZ3ZGYxbCJ9.Bxl18yPka0BUSrGibMTbhg',
    NEXT_PUBLIC_REAL_ESTATE_API_KEY: 'AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914',
    NEXT_PUBLIC_REAL_ESTATE_API_URL: 'https://api.realestateapi.com/v2/'
  },
  async rewrites() {
    // Production-only API routing to custom domain
    const apiBaseUrl = 'https://api.propbolt.com';

    return [
      {
        source: '/api/:path*',
        destination: `${apiBaseUrl}/api/:path*`,
      },
    ];
  },
}

module.exports = nextConfig

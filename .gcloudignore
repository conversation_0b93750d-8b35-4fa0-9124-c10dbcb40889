# Backend-specific .gcloudignore for Go App Engine deployment
# This file is specifically designed to minimize file count for Go backend deployment

# Node.js files (not needed for Go backend)
node_modules/
package.json
package-lock.json
yarn.lock
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js files (not needed for Go backend)
.next/
out/
build/
dist/
src/
public/
next.config.js
tailwind.config.js
postcss.config.js
next-env.d.ts
tsconfig.json

# Frontend configuration
frontend-app.yaml

# Environment files
.env*

# Development tools
.vscode/
.idea/
.history/
.internal/
*.swp
*.swo

# Version control
.git/
.gitignore

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# TypeScript and build artifacts
*.tsbuildinfo
.eslintcache

# Go development files
*.test
*.out
go.work

# Development directories
devtesting/
scripts/

# Documentation
README.md
*.md
platform*.png
DEPLOYMENT_FIX_SUMMARY.md

# Deployment scripts
deploy.sh
start-*.sh
Procfile
transaction.yaml
1.yaml
check-deployment-files.sh
test-deployment.sh

# Frontend-specific ignore files
.gcloudignore-frontend

# Temporary files
pids/
*.pid
*.seed
*.pid.lock
coverage/
*.lcov

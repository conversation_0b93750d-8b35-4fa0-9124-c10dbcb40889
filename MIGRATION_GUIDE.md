# Propbolt Database Migration Guide
## From Neon PostgreSQL to Google Cloud SQL

### Prerequisites
- Google Cloud CLI installed and authenticated
- PostgreSQL client tools (pg_dump, psql)
- Access to current Neon database

### Step 1: Create Cloud SQL Instance

```bash
# Create the Cloud SQL instance
gcloud sql instances create propbolt-db \
    --database-version=POSTGRES_15 \
    --tier=db-f1-micro \
    --region=us-central1 \
    --storage-size=10GB \
    --backup

# Wait for instance to be ready (this may take 5-10 minutes)
gcloud sql instances describe propbolt-db --format="value(state)"
```

### Step 2: Configure Database and User

```bash
# Set root password
gcloud sql users set-password root \
    --instance=propbolt-db \
    --password=PropboltSecure2024!

# Create database
gcloud sql databases create propbolt --instance=propbolt-db

# Create application user
gcloud sql users create propbolt_user \
    --instance=propbolt-db \
    --password=PropboltSecure2024!
```

### Step 3: Export Data from Neon

```bash
# Export schema
pg_dump "postgresql://wonderland_owner:<EMAIL>/wonderland?sslmode=require" \
    --schema-only --no-owner --no-privileges > neon_schema.sql

# Export data
pg_dump "postgresql://wonderland_owner:<EMAIL>/wonderland?sslmode=require" \
    --data-only --no-owner --no-privileges > neon_data.sql
```

### Step 4: Configure Access for Migration

```bash
# Get Cloud SQL IP
CLOUD_SQL_IP=$(gcloud sql instances describe propbolt-db --format="value(ipAddresses[0].ipAddress)")

# Temporarily allow all IPs for migration
gcloud sql instances patch propbolt-db --authorized-networks=0.0.0.0/0 --quiet
```

### Step 5: Import to Cloud SQL

```bash
# Import schema
psql "******************************************************************************************" < neon_schema.sql

# Import data
psql "******************************************************************************************" < neon_data.sql
```

### Step 6: Update Application Configuration

Get the connection name:
```bash
CLOUD_SQL_CONNECTION_NAME=$(gcloud sql instances describe propbolt-db --format="value(connectionName)")
echo $CLOUD_SQL_CONNECTION_NAME
```

Update `app.yaml`:
```yaml
env_variables:
  DATABASE_URL: "*****************************************************************************************"
```

### Step 7: Secure the Database

```bash
# Remove public access
gcloud sql instances patch propbolt-db --authorized-networks="" --quiet
```

### Step 8: Test and Deploy

```bash
# Test the application locally if possible
# Deploy to production
./deploy-production.sh
```

### Verification Checklist

- [ ] Cloud SQL instance created and running
- [ ] Database and user created
- [ ] Schema exported from Neon
- [ ] Data exported from Neon
- [ ] Schema imported to Cloud SQL
- [ ] Data imported to Cloud SQL
- [ ] Application configuration updated
- [ ] Database access secured
- [ ] Application deployed and tested

### Rollback Plan

If migration fails:
1. Revert `app.yaml` to use original Neon connection
2. Redeploy application
3. Delete Cloud SQL instance if needed

### Connection Details

- **Instance Name**: propbolt-db
- **Database Name**: propbolt
- **Username**: propbolt_user
- **Password**: PropboltSecure2024!
- **Connection for App Engine**: `*****************************************************************************************`

### Important Notes

1. The migration script temporarily opens the database to all IPs for data transfer
2. Access is restricted after migration completes
3. App Engine connects via Unix socket, not TCP
4. Backup the original configuration files before making changes
5. Test authentication and vacant land search functionality after migration

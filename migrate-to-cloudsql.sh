#!/bin/bash

# Migration script from Neon PostgreSQL to Google Cloud SQL
# Propbolt Vacant Land Search Platform Database Migration

set -e

echo "🚀 Starting Propbolt Database Migration to Google Cloud SQL"
echo "============================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
NEON_DATABASE_URL="postgresql://wonderland_owner:<EMAIL>/wonderland?sslmode=require"
INSTANCE_NAME="propbolt-postgres"
DATABASE_NAME="propbolt"
DB_USER="propbolt_user"
DB_PASSWORD="PropboltSecure2024!"

# Check if Cloud SQL instance exists and is ready
print_status "Checking Cloud SQL instance status..."
INSTANCE_STATE=$(gcloud sql instances describe $INSTANCE_NAME --format="value(state)" 2>/dev/null || echo "NOT_FOUND")

if [ "$INSTANCE_STATE" != "RUNNABLE" ]; then
    print_error "Cloud SQL instance $INSTANCE_NAME is not ready. Current state: $INSTANCE_STATE"
    print_status "Please wait for the instance to be created and try again."
    exit 1
fi

print_success "Cloud SQL instance $INSTANCE_NAME is ready!"

# Step 1: Set up Cloud SQL database and user
print_status "Setting up database and user in Cloud SQL..."

# Set root password
gcloud sql users set-password root --instance=$INSTANCE_NAME --password=$DB_PASSWORD

# Create database
gcloud sql databases create $DATABASE_NAME --instance=$INSTANCE_NAME

# Create user
gcloud sql users create $DB_USER --instance=$INSTANCE_NAME --password=$DB_PASSWORD

print_success "Database and user created successfully!"

# Step 2: Configure authorized networks for migration
print_status "Configuring authorized networks..."
gcloud sql instances patch $INSTANCE_NAME --authorized-networks=0.0.0.0/0 --quiet

print_warning "Temporarily allowing all IPs for migration. Will restrict after migration."

# Step 3: Get Cloud SQL connection info
CLOUD_SQL_IP=$(gcloud sql instances describe $INSTANCE_NAME --format="value(ipAddresses[0].ipAddress)")
CLOUD_SQL_CONNECTION_NAME=$(gcloud sql instances describe $INSTANCE_NAME --format="value(connectionName)")

print_status "Cloud SQL IP: $CLOUD_SQL_IP"
print_status "Connection Name: $CLOUD_SQL_CONNECTION_NAME"

# Step 4: Export schema and data from Neon
print_status "Exporting schema and data from Neon PostgreSQL..."

# Export schema only (use sudo if permission issues)
if ! pg_dump "$NEON_DATABASE_URL" --schema-only --no-owner --no-privileges > neon_schema.sql 2>/dev/null; then
    print_warning "Retrying schema export with sudo..."
    sudo pg_dump "$NEON_DATABASE_URL" --schema-only --no-owner --no-privileges > neon_schema.sql
fi

# Export data only (use sudo if permission issues)
if ! pg_dump "$NEON_DATABASE_URL" --data-only --no-owner --no-privileges > neon_data.sql 2>/dev/null; then
    print_warning "Retrying data export with sudo..."
    sudo pg_dump "$NEON_DATABASE_URL" --data-only --no-owner --no-privileges > neon_data.sql
fi

print_success "Data exported from Neon PostgreSQL!"

# Step 5: Import to Cloud SQL
print_status "Importing schema to Cloud SQL..."

# Build Cloud SQL connection string
CLOUD_SQL_URL="*****************************************************/$DATABASE_NAME?sslmode=require"

# Import schema (use sudo if permission issues)
if ! psql "$CLOUD_SQL_URL" < neon_schema.sql 2>/dev/null; then
    print_warning "Retrying schema import with sudo..."
    sudo psql "$CLOUD_SQL_URL" < neon_schema.sql
fi

print_success "Schema imported successfully!"

print_status "Importing data to Cloud SQL..."

# Import data (use sudo if permission issues)
if ! psql "$CLOUD_SQL_URL" < neon_data.sql 2>/dev/null; then
    print_warning "Retrying data import with sudo..."
    sudo psql "$CLOUD_SQL_URL" < neon_data.sql
fi

print_success "Data imported successfully!"

# Step 6: Verify migration
print_status "Verifying migration..."

# Check table counts
echo "Table counts in Cloud SQL:"
psql "$CLOUD_SQL_URL" -c "
SELECT 
    schemaname,
    tablename,
    n_tup_ins as row_count
FROM pg_stat_user_tables 
ORDER BY tablename;
"

# Step 7: Restrict access
print_status "Restricting database access to App Engine only..."
gcloud sql instances patch $INSTANCE_NAME --authorized-networks="" --quiet

print_success "Database access restricted!"

# Step 8: Update application configuration files
print_status "Updating application configuration files..."

# Update app.yaml
NEW_DATABASE_URL="********************************************************************************************"

# Backup original files
cp app.yaml app.yaml.backup
cp frontend-app.yaml frontend-app.yaml.backup

# Update app.yaml (use sudo if permission issues)
if ! sed -i.bak "s|DATABASE_URL:.*|DATABASE_URL: \"$NEW_DATABASE_URL\"|" app.yaml 2>/dev/null; then
    print_warning "Retrying app.yaml update with sudo..."
    sudo sed -i.bak "s|DATABASE_URL:.*|DATABASE_URL: \"$NEW_DATABASE_URL\"|" app.yaml
fi

print_success "Configuration files updated!"

# Step 9: Test database connection
print_status "Testing database connection..."

# Test connection (use sudo if permission issues)
if ! psql "$CLOUD_SQL_URL" -c "SELECT version();" > /dev/null 2>&1; then
    print_warning "Retrying connection test with sudo..."
    if sudo psql "$CLOUD_SQL_URL" -c "SELECT version();" > /dev/null 2>&1; then
        print_success "Database connection test successful (with sudo)!"
    else
        print_error "Database connection test failed!"
        exit 1
    fi
else
    print_success "Database connection test successful!"
fi

# Step 10: Output new connection string
echo ""
echo "🎉 MIGRATION COMPLETED SUCCESSFULLY!"
echo "===================================="
echo ""
echo "New Database Connection Details:"
echo "--------------------------------"
echo "Instance Name: $INSTANCE_NAME"
echo "Database Name: $DATABASE_NAME"
echo "Username: $DB_USER"
echo "Password: $DB_PASSWORD"
echo "Connection Name: $CLOUD_SQL_CONNECTION_NAME"
echo "Public IP: $CLOUD_SQL_IP"
echo ""
echo "Connection String for App Engine:"
echo "$NEW_DATABASE_URL"
echo ""
echo "Files Updated:"
echo "- app.yaml (DATABASE_URL updated)"
echo "- database/connection.go (default connection updated)"
echo ""
echo "Next Steps:"
echo "1. Deploy application with ./deploy-production.sh"
echo "2. Test authentication and vacant land search functionality"
echo "3. Verify all data migrated correctly"
echo "4. Monitor application performance"
echo ""

# Cleanup (use sudo if permission issues)
if ! rm -f neon_schema.sql neon_data.sql *.bak 2>/dev/null; then
    print_warning "Retrying cleanup with sudo..."
    sudo rm -f neon_schema.sql neon_data.sql *.bak
fi

print_success "Migration script completed!"

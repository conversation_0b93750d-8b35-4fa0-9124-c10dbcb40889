export interface Property {
  id: number;
  address: string;
  price: number;
  size: string;
  zoning: string;
  latitude: number;
  longitude: number;
  description: string;
  habitability: string;
  proximity: string;
  chainLeasePotential: string;
  daysOnMarket: number;
  pricePerSqFt: number;
  createdAt: string;
  updatedAt: string;
}

export interface PropertySearchFilters {
  zoning?: string;
  minPrice?: number;
  maxPrice?: number;
  chainPotential?: string;
  location?: string; // Added location filter for city/state selection
}

export interface PropertySearchRequest {
  query: string;
  filters: PropertySearchFilters;
}

export interface PropertySearchResponse {
  results: Property[];
  total: number;
}

export interface DashboardStats {
  totalProperties: number;
  averagePrice: number;
  newListings: number;
  soldThisMonth: number;
}

export interface SyncResponse {
  syncedCount: number;
  message: string;
}

export interface MapBounds {
  north: number;
  east: number;
  south: number;
  west: number;
}

export interface PropertyMarker {
  id: number;
  lat: number;
  lng: number;
  price: number;
  address: string;
  zoning: string;
}

export interface RealEstateAPIProperty {
  parcelId: string;
  address: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  boundaries?: {
    type: string;
    coordinates: number[][][];
  };
  zoning?: string;
  landUse?: string;
  acreage?: number;
}

'use client';

import React, { useState } from 'react';
import { X, Filter } from 'lucide-react';
import type { PropertySearchFilters } from '@/types/property';

interface AdvancedFiltersProps {
  onApply: (filters: PropertySearchFilters) => void;
  onClose: () => void;
}

export function AdvancedFilters({ onApply, onClose }: AdvancedFiltersProps) {
  const [filters, setFilters] = useState<PropertySearchFilters>({});

  const handleFilterChange = (key: keyof PropertySearchFilters, value: string | number) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined,
    }));
  };

  const handleApply = () => {
    onApply(filters);
  };

  const handleReset = () => {
    setFilters({});
  };

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <Filter size={20} className="text-zillow-blue" />
          <h3 className="text-lg font-semibold text-zillow-dark-gray">
            Advanced Vacant Land Filters
          </h3>
        </div>
        
        <button
          onClick={onClose}
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <X size={20} />
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
        {/* Zoning Type */}
        <div>
          <label className="block text-sm font-medium text-zillow-dark-gray mb-2">
            Zoning Type
          </label>
          <select
            value={filters.zoning || ''}
            onChange={(e) => handleFilterChange('zoning', e.target.value)}
            className="select-field"
          >
            <option value="">All Zoning Types</option>
            <option value="Commercial">Commercial</option>
            <option value="Residential">Residential</option>
            <option value="Industrial">Industrial</option>
            <option value="Mixed Use">Mixed Use</option>
            <option value="Agricultural">Agricultural</option>
          </select>
        </div>

        {/* Price Range */}
        <div>
          <label className="block text-sm font-medium text-zillow-dark-gray mb-2">
            Minimum Price
          </label>
          <select
            value={filters.minPrice || ''}
            onChange={(e) => handleFilterChange('minPrice', parseInt(e.target.value))}
            className="select-field"
          >
            <option value="">No Minimum</option>
            <option value="25000">$25,000</option>
            <option value="50000">$50,000</option>
            <option value="75000">$75,000</option>
            <option value="100000">$100,000</option>
            <option value="150000">$150,000</option>
            <option value="200000">$200,000</option>
            <option value="300000">$300,000</option>
            <option value="500000">$500,000</option>
            <option value="750000">$750,000</option>
            <option value="1000000">$1,000,000</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-zillow-dark-gray mb-2">
            Maximum Price
          </label>
          <select
            value={filters.maxPrice || ''}
            onChange={(e) => handleFilterChange('maxPrice', parseInt(e.target.value))}
            className="select-field"
          >
            <option value="">No Maximum</option>
            <option value="50000">$50,000</option>
            <option value="75000">$75,000</option>
            <option value="100000">$100,000</option>
            <option value="150000">$150,000</option>
            <option value="200000">$200,000</option>
            <option value="300000">$300,000</option>
            <option value="500000">$500,000</option>
            <option value="750000">$750,000</option>
            <option value="1000000">$1,000,000</option>
            <option value="2000000">$2,000,000</option>
            <option value="5000000">$5,000,000</option>
          </select>
        </div>

        {/* Chain Lease Potential */}
        <div>
          <label className="block text-sm font-medium text-zillow-dark-gray mb-2">
            Chain Lease Potential
          </label>
          <select
            value={filters.chainPotential || ''}
            onChange={(e) => handleFilterChange('chainPotential', e.target.value)}
            className="select-field"
          >
            <option value="">All Potential Levels</option>
            <option value="Very High">Very High</option>
            <option value="High">High</option>
            <option value="Medium">Medium</option>
            <option value="Low">Low</option>
            <option value="None">None</option>
          </select>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between pt-6 border-t border-zillow-border">
        <button
          onClick={handleReset}
          className="btn-secondary"
        >
          Reset Filters
        </button>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleApply}
            className="btn-primary"
          >
            Apply Filters
          </button>
        </div>
      </div>
    </div>
  );
}

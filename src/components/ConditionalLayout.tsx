'use client';

import React from 'react';
import { useSession } from 'next-auth/react';
import { usePathname } from 'next/navigation';
import { Navbar } from '@/components/Navbar';

interface ConditionalLayoutProps {
  children: React.ReactNode;
}

export function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const { data: session, status } = useSession();
  const pathname = usePathname();

  // Don't show navbar on auth pages
  const isAuthPage = pathname.startsWith('/auth/');
  
  // Show navbar only if user is authenticated and not on auth pages
  const showNavbar = !isAuthPage && status === 'authenticated' && session;

  if (showNavbar) {
    return (
      <div className="min-h-screen bg-zillow-gray">
        <Navbar />
        <main className="ml-0 lg:ml-80 min-h-screen">
          {children}
        </main>
      </div>
    );
  }

  // No navbar layout for unauthenticated users or auth pages
  return (
    <div className="min-h-screen bg-zillow-gray">
      <main className="min-h-screen">
        {children}
      </main>
    </div>
  );
}

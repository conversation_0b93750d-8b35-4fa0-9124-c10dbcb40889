'use client';

import React, { createContext, useContext, useState, useCallback } from 'react';
import { SessionProvider } from 'next-auth/react';

interface NotificationContextType {
  showNotification: (message: string, type?: 'success' | 'error' | 'info') => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export function useNotification() {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  return context;
}

interface Notification {
  id: string;
  message: string;
  type: 'success' | 'error' | 'info';
}

export function Providers({ children }: { children: React.ReactNode }) {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  const showNotification = useCallback((message: string, type: 'success' | 'error' | 'info' = 'info') => {
    const id = Math.random().toString(36).substr(2, 9);
    const notification = { id, message, type };
    
    setNotifications(prev => [...prev, notification]);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== id));
    }, 5000);
  }, []);

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  return (
    <SessionProvider>
      <NotificationContext.Provider value={{ showNotification }}>
        {children}

        {/* Notification Container */}
        <div className="fixed top-5 right-5 z-50 space-y-2">
          {notifications.map((notification) => (
            <div
              key={notification.id}
              className={`notification notification-${notification.type} animate-slide-in`}
              onClick={() => removeNotification(notification.id)}
            >
              <div className="flex items-center justify-between">
                <span>{notification.message}</span>
                <button
                  onClick={() => removeNotification(notification.id)}
                  className="ml-4 text-white hover:text-gray-200"
                >
                  ×
                </button>
              </div>
            </div>
          ))}
        </div>
      </NotificationContext.Provider>
    </SessionProvider>
  );
}

'use client';

import React, { useState, useEffect } from 'react';
import { formatDate, formatTime } from '@/lib/utils';

export function DashboardHeader() {
  const [currentDate, setCurrentDate] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentDate(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <section className="card">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div className="mb-4 lg:mb-0">
          <h1 className="text-3xl font-bold text-zillow-dark-gray mb-2">
            Vacant Land Admin Dashboard
          </h1>
          <p className="text-gray-600">
            Manage vacant land properties for sale in Florida
          </p>
        </div>
        
        <div className="text-right text-gray-600">
          <div className="text-lg font-medium">
            {formatDate(currentDate)}
          </div>
          <div className="text-sm">
            {formatTime(currentDate)}
          </div>
        </div>
      </div>
    </section>
  );
}

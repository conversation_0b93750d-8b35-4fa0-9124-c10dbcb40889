'use client';

import React, { useState } from 'react';
import { Search, RefreshCw } from 'lucide-react';
import type { PropertySearchFilters } from '@/types/property';

interface SearchSectionProps {
  onSearch: (query: string, filters: PropertySearchFilters) => void;
  onSyncData: () => void;
  isSearching: boolean;
}

export function SearchSection({ onSearch, onSyncData, isSearching }: SearchSectionProps) {
  const [query, setQuery] = useState('');
  const [filters, setFilters] = useState<PropertySearchFilters>({
    location: 'Daytona Beach, FL' // Default to Daytona Beach
  });
  const [isSyncing, setIsSyncing] = useState(false);
  const [lastSync, setLastSync] = useState<string>('Never');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(query, filters);
  };

  const handleSyncClick = async () => {
    setIsSyncing(true);
    try {
      await onSyncData();
      setLastSync(new Date().toLocaleTimeString());
    } finally {
      setIsSyncing(false);
    }
  };

  const handleFilterChange = (key: keyof PropertySearchFilters, value: string | number) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined,
    }));
  };

  return (
    <section className="card">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
        <h2 className="text-xl font-semibold text-zillow-dark-gray mb-4 lg:mb-0">
          Vacant Land Search - Florida Properties
        </h2>
        
        <div className="flex items-center space-x-4">
          <button
            onClick={handleSyncClick}
            disabled={isSyncing}
            className={`btn-success flex items-center space-x-2 ${
              isSyncing ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            <RefreshCw size={16} className={isSyncing ? 'animate-spin' : ''} />
            <span>{isSyncing ? 'Syncing...' : 'Sync Live Data'}</span>
          </button>
          
          <span className="text-xs text-gray-500">
            Last sync: {lastSync}
          </span>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Location and Search */}
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <label className="block text-sm font-medium text-zillow-dark-gray mb-2">
              Location
            </label>
            <select
              value={filters.location || 'Daytona Beach, FL'}
              onChange={(e) => handleFilterChange('location', e.target.value)}
              className="select-field"
            >
              <option value="Daytona Beach, FL">Daytona Beach, FL</option>
              <option value="Ormond Beach, FL">Ormond Beach, FL</option>
              <option value="Port Orange, FL">Port Orange, FL</option>
              <option value="New Smyrna Beach, FL">New Smyrna Beach, FL</option>
              <option value="DeLand, FL">DeLand, FL</option>
            </select>
          </div>
          <div className="flex-1">
            <label className="block text-sm font-medium text-zillow-dark-gray mb-2">
              Search Keywords
            </label>
            <input
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Search by address, area, or keywords..."
              className="input-field"
            />
          </div>
          <div className="flex items-end">
            <button
              type="submit"
              disabled={isSearching}
              className={`btn-primary flex items-center space-x-2 ${
                isSearching ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              <Search size={16} />
              <span>{isSearching ? 'Searching...' : 'Search Vacant Land'}</span>
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-zillow-dark-gray mb-2">
              Zoning Type
            </label>
            <select
              value={filters.zoning || ''}
              onChange={(e) => handleFilterChange('zoning', e.target.value)}
              className="select-field"
            >
              <option value="">All Zoning</option>
              <option value="Commercial">Commercial</option>
              <option value="Residential">Residential</option>
              <option value="Industrial">Industrial</option>
              <option value="Mixed Use">Mixed Use</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-zillow-dark-gray mb-2">
              Min Price
            </label>
            <select
              value={filters.minPrice || ''}
              onChange={(e) => handleFilterChange('minPrice', parseInt(e.target.value))}
              className="select-field"
            >
              <option value="">No Min</option>
              <option value="50000">$50,000</option>
              <option value="100000">$100,000</option>
              <option value="200000">$200,000</option>
              <option value="500000">$500,000</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-zillow-dark-gray mb-2">
              Max Price
            </label>
            <select
              value={filters.maxPrice || ''}
              onChange={(e) => handleFilterChange('maxPrice', parseInt(e.target.value))}
              className="select-field"
            >
              <option value="">No Max</option>
              <option value="100000">$100,000</option>
              <option value="200000">$200,000</option>
              <option value="500000">$500,000</option>
              <option value="1000000">$1,000,000</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-zillow-dark-gray mb-2">
              Chain Potential
            </label>
            <select
              value={filters.chainPotential || ''}
              onChange={(e) => handleFilterChange('chainPotential', e.target.value)}
              className="select-field"
            >
              <option value="">All Potential</option>
              <option value="Very High">Very High</option>
              <option value="High">High</option>
              <option value="Medium">Medium</option>
              <option value="Low">Low</option>
            </select>
          </div>
        </div>
      </form>
    </section>
  );
}

'use client';

import React, { useEffect, useRef, useState } from 'react';
import mapboxgl from 'mapbox-gl';
import { formatPrice, isValidCoordinate, filterPropertiesWithValidCoords } from '@/lib/utils';
import { DAYTONA_BEACH_CENTER } from '@/lib/utils';
import type { Property } from '@/types/property';

// Set Mapbox access token
mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_TOKEN || '';

interface MapComponentProps {
  properties: Property[];
  selectedProperty: Property | null;
  onPropertyClick: (propertyId: number) => void;
}

export function MapComponent({ properties, selectedProperty, onPropertyClick }: MapComponentProps) {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const markers = useRef<mapboxgl.Marker[]>([]);
  const [isMapLoaded, setIsMapLoaded] = useState(false);

  // Initialize map
  useEffect(() => {
    if (!mapContainer.current || map.current) return;

    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: 'mapbox://styles/mapbox/light-v11',
      center: [DAYTONA_BEACH_CENTER.lng, DAYTONA_BEACH_CENTER.lat],
      zoom: 12,
    });

    map.current.on('load', () => {
      setIsMapLoaded(true);
    });

    // Add navigation controls
    map.current.addControl(new mapboxgl.NavigationControl(), 'top-right');

    return () => {
      if (map.current) {
        map.current.remove();
        map.current = null;
      }
    };
  }, []);

  // Update markers when properties change
  useEffect(() => {
    if (!map.current || !isMapLoaded) return;

    // Clear existing markers
    markers.current.forEach(marker => marker.remove());
    markers.current = [];

    // Filter properties with valid coordinates
    const validProperties = filterPropertiesWithValidCoords(properties);

    // Add new markers for valid properties
    validProperties.forEach(property => {
      // Create custom marker element
      const markerElement = document.createElement('div');
      markerElement.className = 'custom-marker';
      markerElement.innerHTML = `
        <div class="bg-zillow-blue text-white px-2 py-1 rounded-lg shadow-lg text-sm font-medium cursor-pointer hover:bg-zillow-blue-dark transition-colors">
          ${formatPrice(property.price)}
        </div>
      `;

      // Create popup
      const popup = new mapboxgl.Popup({
        offset: 25,
        closeButton: true,
        closeOnClick: false,
      }).setHTML(`
        <div class="p-4 max-w-sm">
          <h4 class="font-bold text-zillow-blue mb-2">${formatPrice(property.price)}</h4>
          <p class="font-medium mb-2">${property.address}</p>
          <p class="text-sm text-gray-600 mb-3">${property.description}</p>
          <div class="grid grid-cols-2 gap-2 text-xs text-gray-600">
            <div><strong>Size:</strong> ${property.size}</div>
            <div><strong>Zoning:</strong> ${property.zoning}</div>
            <div><strong>Chain Potential:</strong> ${property.chainLeasePotential}</div>
            <div><strong>Proximity:</strong> ${property.proximity}</div>
          </div>
        </div>
      `);

      // Create marker with error handling
      try {
        const marker = new mapboxgl.Marker(markerElement)
          .setLngLat([property.longitude, property.latitude])
          .setPopup(popup)
          .addTo(map.current!);

        // Add click handler
        markerElement.addEventListener('click', () => {
          onPropertyClick(property.id);
        });

        markers.current.push(marker);
      } catch (error) {
        console.warn(`Failed to create marker for property ${property.id}:`, error);
      }
    });

    // Fit map to show all markers with proper error handling
    if (validProperties.length > 0) {
      try {
        const bounds = new mapboxgl.LngLatBounds();
        let hasValidBounds = false;

        validProperties.forEach(property => {
          try {
            bounds.extend([property.longitude, property.latitude]);
            hasValidBounds = true;
          } catch (error) {
            console.warn(`Failed to extend bounds for property ${property.id}:`, error);
          }
        });

        // Only fit bounds if we have valid bounds
        if (hasValidBounds && bounds.getNorthEast() && bounds.getSouthWest()) {
          map.current.fitBounds(bounds, {
            padding: 50,
            maxZoom: 15,
            duration: 1000,
          });
        } else {
          // Fallback to Daytona Beach center if no valid bounds
          console.warn('No valid bounds found, centering on Daytona Beach');
          map.current.flyTo({
            center: [DAYTONA_BEACH_CENTER.lng, DAYTONA_BEACH_CENTER.lat],
            zoom: 12,
            duration: 1000,
          });
        }
      } catch (error) {
        console.error('Error fitting bounds:', error);
        // Fallback to Daytona Beach center
        map.current.flyTo({
          center: [DAYTONA_BEACH_CENTER.lng, DAYTONA_BEACH_CENTER.lat],
          zoom: 12,
          duration: 1000,
        });
      }
    } else if (properties.length > 0) {
      // If we have properties but none with valid coordinates, show a warning
      console.warn(`Found ${properties.length} properties but none have valid coordinates`);
      // Center on Daytona Beach
      map.current.flyTo({
        center: [DAYTONA_BEACH_CENTER.lng, DAYTONA_BEACH_CENTER.lat],
        zoom: 12,
        duration: 1000,
      });
    }
  }, [properties, isMapLoaded, onPropertyClick]);

  // Handle selected property
  useEffect(() => {
    if (!map.current || !selectedProperty) return;

    // Validate selected property coordinates
    const hasValidCoords = isValidCoordinate(selectedProperty.latitude, selectedProperty.longitude);

    if (!hasValidCoords) {
      console.warn(`Selected property ${selectedProperty.id} has invalid coordinates:`, {
        lat: selectedProperty.latitude,
        lng: selectedProperty.longitude
      });
      return;
    }

    try {
      // Center map on selected property
      map.current.flyTo({
        center: [selectedProperty.longitude, selectedProperty.latitude],
        zoom: 16,
        duration: 1000,
      });

      // Open popup for selected property
      const selectedMarker = markers.current.find(marker => {
        try {
          const lngLat = marker.getLngLat();
          return Math.abs(lngLat.lat - selectedProperty.latitude) < 0.0001 &&
                 Math.abs(lngLat.lng - selectedProperty.longitude) < 0.0001;
        } catch (error) {
          console.warn('Error getting marker coordinates:', error);
          return false;
        }
      });

      if (selectedMarker) {
        try {
          selectedMarker.togglePopup();
        } catch (error) {
          console.warn('Error toggling popup:', error);
        }
      }
    } catch (error) {
      console.error('Error handling selected property:', error);
    }
  }, [selectedProperty]);

  if (!process.env.NEXT_PUBLIC_MAPBOX_TOKEN) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <p className="text-gray-600 mb-2">Map unavailable</p>
          <p className="text-sm text-gray-500">Mapbox token not configured</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full relative">
      <div ref={mapContainer} className="w-full h-full" />
      
      {!isMapLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="loading-spinner" />
        </div>
      )}
    </div>
  );
}

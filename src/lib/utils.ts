import { type ClassValue, clsx } from 'clsx';

export function cn(...inputs: ClassValue[]) {
  return clsx(inputs);
}

export function formatPrice(price: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price);
}

export function formatNumber(num: number): string {
  return new Intl.NumberFormat('en-US').format(num);
}

export function formatDate(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(dateObj);
}

export function formatTime(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    second: '2-digit',
  }).format(dateObj);
}

export function getZoningColor(zoning: string): string {
  const zoningLower = zoning.toLowerCase();
  
  if (zoningLower.includes('commercial')) {
    return 'bg-zillow-blue-light text-zillow-blue';
  } else if (zoningLower.includes('residential')) {
    return 'bg-yellow-100 text-yellow-700';
  } else if (zoningLower.includes('industrial')) {
    return 'bg-purple-100 text-purple-700';
  } else if (zoningLower.includes('mixed')) {
    return 'bg-green-100 text-green-700';
  }
  
  return 'bg-gray-100 text-gray-700';
}

export function getChainPotentialColor(potential: string): string {
  const potentialLower = potential.toLowerCase();
  
  if (potentialLower.includes('very high')) {
    return 'bg-green-100 text-green-800';
  } else if (potentialLower.includes('high')) {
    return 'bg-green-50 text-green-700';
  } else if (potentialLower.includes('medium')) {
    return 'bg-yellow-100 text-yellow-700';
  } else if (potentialLower.includes('low')) {
    return 'bg-red-100 text-red-700';
  }
  
  return 'bg-gray-100 text-gray-700';
}

export function calculateDistance(
  lat1: number,
  lng1: number,
  lat2: number,
  lng2: number
): number {
  const R = 3959; // Earth's radius in miles
  const dLat = (lat2 - lat1) * (Math.PI / 180);
  const dLng = (lng2 - lng1) * (Math.PI / 180);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * (Math.PI / 180)) *
      Math.cos(lat2 * (Math.PI / 180)) *
      Math.sin(dLng / 2) *
      Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

// Daytona Beach coordinates for proximity calculations
export const DAYTONA_BEACH_CENTER = {
  lat: 29.2108,
  lng: -81.0228,
};

export const ATLANTIC_OCEAN_COORDS = {
  lat: 29.2108,
  lng: -80.9773, // Approximate ocean coordinates
};

export function calculateProximityToBeach(lat: number, lng: number): string {
  const distance = calculateDistance(lat, lng, ATLANTIC_OCEAN_COORDS.lat, ATLANTIC_OCEAN_COORDS.lng);
  
  if (distance < 0.5) {
    return 'Beachfront';
  } else if (distance < 1) {
    return `${distance.toFixed(1)} miles to beach`;
  } else if (distance < 5) {
    return `${distance.toFixed(1)} miles to beach`;
  } else {
    return `${distance.toFixed(0)} miles to beach`;
  }
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Validates if coordinates are valid for mapping
 * @param lat Latitude value
 * @param lng Longitude value
 * @returns true if coordinates are valid, false otherwise
 */
export function isValidCoordinate(lat: number | null | undefined, lng: number | null | undefined): boolean {
  return lat != null &&
    lng != null &&
    !isNaN(lat) &&
    !isNaN(lng) &&
    lat !== 0 &&
    lng !== 0 &&
    Math.abs(lat) <= 90 &&
    Math.abs(lng) <= 180;
}

/**
 * Filters properties to only include those with valid coordinates
 * @param properties Array of properties to filter
 * @returns Array of properties with valid coordinates
 */
export function filterPropertiesWithValidCoords<T extends { latitude: number; longitude: number }>(
  properties: T[]
): T[] {
  return properties.filter(property =>
    isValidCoordinate(property.latitude, property.longitude)
  );
}

/**
 * Safely creates map bounds from an array of coordinates
 * @param coordinates Array of [lng, lat] coordinate pairs
 * @returns Valid bounds object or null if no valid coordinates
 */
export function createSafeBounds(coordinates: [number, number][]): {
  isValid: boolean;
  bounds?: { north: number; south: number; east: number; west: number }
} {
  const validCoords = coordinates.filter(([lng, lat]) =>
    isValidCoordinate(lat, lng)
  );

  if (validCoords.length === 0) {
    return { isValid: false };
  }

  const lats = validCoords.map(([, lat]) => lat);
  const lngs = validCoords.map(([lng]) => lng);

  return {
    isValid: true,
    bounds: {
      north: Math.max(...lats),
      south: Math.min(...lats),
      east: Math.max(...lngs),
      west: Math.min(...lngs),
    }
  };
}

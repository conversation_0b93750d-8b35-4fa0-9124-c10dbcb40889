import { NextResponse } from 'next/server';
import { SyncResponse } from '@/types/property';

const API_BASE_URL = 'https://api.propbolt.com';

export async function POST() {
  try {
    const response = await fetch(`${API_BASE_URL}/api/sync-properties`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Backend API error: ${response.status} ${response.statusText}`);
    }

    const data: SyncResponse = await response.json();
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in sync properties API route:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to sync properties',
        message: error instanceof Error ? error.message : 'Unknown error',
        syncedCount: 0
      },
      { status: 500 }
    );
  }
}

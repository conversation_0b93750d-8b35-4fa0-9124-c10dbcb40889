import { NextResponse } from 'next/server';
import { DashboardStats } from '@/types/property';

const API_BASE_URL = 'https://api.propbolt.com';

export async function GET() {
  try {
    const response = await fetch(`${API_BASE_URL}/api/dashboard/stats`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Backend API error: ${response.status} ${response.statusText}`);
    }

    const data: DashboardStats = await response.json();
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in dashboard stats API route:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to load dashboard stats',
        message: error instanceof Error ? error.message : 'Unknown error',
        totalProperties: 0,
        averagePrice: 0,
        newListings: 0,
        soldThisMonth: 0
      },
      { status: 500 }
    );
  }
}

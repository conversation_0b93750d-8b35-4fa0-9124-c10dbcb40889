import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { ConditionalLayout } from '@/components/ConditionalLayout';
import { Providers } from '@/components/Providers';
import { ErrorBoundary } from '@/components/ErrorBoundary';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Vacant Land Search - Daytona Beach FL | Admin Dashboard',
  description: 'Professional real estate admin panel for vacant land search in Daytona Beach, Florida. Find commercial and residential zoning opportunities with chain lease potential.',
  keywords: 'vacant land, Daytona Beach, real estate, commercial zoning, residential zoning, chain lease, property search',
  authors: [{ name: 'Vacant Land Search Team' }],
  viewport: 'width=device-width, initial-scale=1',
  robots: 'index, follow',
  openGraph: {
    title: 'Vacant Land Search - Daytona Beach FL',
    description: 'Professional real estate admin panel for vacant land opportunities',
    type: 'website',
    locale: 'en_US',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        <script
          src={`https://api.mapbox.com/mapbox-gl-js/v3.0.1/mapbox-gl.js`}
          async
        />
        <link
          href="https://api.mapbox.com/mapbox-gl-js/v3.0.1/mapbox-gl.css"
          rel="stylesheet"
        />
      </head>
      <body className={inter.className}>
        <ErrorBoundary>
          <Providers>
            <ConditionalLayout>
              {children}
            </ConditionalLayout>
          </Providers>
        </ErrorBoundary>
      </body>
    </html>
  );
}

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { DashboardHeader } from '@/components/DashboardHeader';
import { StatsGrid } from '@/components/StatsGrid';
import { SearchSection } from '@/components/SearchSection';
import { MapAndResults } from '@/components/MapAndResults';
import { propertyAPI } from '@/lib/api';
import { useNotification } from '@/components/Providers';
import type { DashboardStats, Property, PropertySearchFilters } from '@/types/property';

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [properties, setProperties] = useState<Property[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSearching, setIsSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<PropertySearchFilters>({});
  const [error, setError] = useState<string | null>(null);
  const { showNotification } = useNotification();

  const loadDashboardData = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const [statsData, searchResults] = await Promise.all([
        propertyAPI.getDashboardStats(),
        propertyAPI.searchProperties({ query: '', filters: {} }),
      ]);

      setStats(statsData);
      setProperties(searchResults.results);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to load dashboard data';
      setError(errorMessage);
      showNotification('Failed to load dashboard data', 'error');
    } finally {
      setIsLoading(false);
    }
  }, [showNotification]);

  // Load initial data
  useEffect(() => {
    loadDashboardData();
  }, [loadDashboardData]);

  const handleSearch = async (query: string, searchFilters: PropertySearchFilters) => {
    try {
      setIsSearching(true);
      setSearchQuery(query);
      setFilters(searchFilters);
      
      const results = await propertyAPI.searchProperties({
        query,
        filters: searchFilters,
      });
      
      setProperties(results.results);
    } catch (error) {
      console.error('Error searching properties:', error);
      showNotification('Search failed. Please try again.', 'error');
    } finally {
      setIsSearching(false);
    }
  };

  const handleSyncData = async () => {
    try {
      const result = await propertyAPI.syncProperties();
      showNotification(`Successfully synced ${result.syncedCount} properties!`, 'success');
      
      // Reload dashboard data after sync
      await loadDashboardData();
    } catch (error) {
      console.error('Error syncing data:', error);
      showNotification('Failed to sync data. Please try again.', 'error');
    }
  };

  if (error) {
    return (
      <div className="p-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-red-500 text-4xl mb-4">⚠️</div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Dashboard</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => loadDashboardData()}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="p-8">
        <div className="flex items-center justify-center h-64">
          <div className="loading-spinner" />
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 space-y-8">
      <DashboardHeader />
      
      {stats && <StatsGrid stats={stats} />}
      
      <SearchSection
        onSearch={handleSearch}
        onSyncData={handleSyncData}
        isSearching={isSearching}
      />
      
      <MapAndResults
        properties={properties}
        searchQuery={searchQuery}
        filters={filters}
      />
    </div>
  );
}

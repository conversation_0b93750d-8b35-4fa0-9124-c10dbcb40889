import { withAuth } from 'next-auth/middleware';
import { NextResponse } from 'next/server';

// Authentication middleware - protects all routes except auth pages
export default withAuth(
  function middleware(req) {
    // Allow access to auth pages without authentication
    if (req.nextUrl.pathname.startsWith('/auth/')) {
      // If user is already authenticated and tries to access signin, redirect to dashboard
      if (req.nextauth.token && req.nextUrl.pathname === '/auth/signin') {
        return NextResponse.redirect(new URL('/', req.url));
      }
      return NextResponse.next();
    }

    // Redirect to sign-in if not authenticated
    if (!req.nextauth.token) {
      const signInUrl = new URL('/auth/signin', req.url);
      signInUrl.searchParams.set('callbackUrl', req.url);
      return NextResponse.redirect(signInUrl);
    }

    // Validate account type for authenticated users
    if (req.nextauth.token && req.nextauth.token.accountType !== 'land') {
      return NextResponse.redirect(new URL('/auth/signin?error=access_denied', req.url));
    }

    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // Allow access to auth pages
        if (req.nextUrl.pathname.startsWith('/auth/')) {
          return true;
        }

        // Require authentication and valid account type for all other pages
        return !!token && token.accountType === 'land';
      },
    },
  }
);

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (all API routes including NextAuth)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',
  ],
};
